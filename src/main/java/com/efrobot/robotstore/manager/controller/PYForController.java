package com.efrobot.robotstore.manager.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.efrobot.robotstore.baseapi.manager.fabo.StudentMapper;
import com.efrobot.robotstore.baseapi.manager.fabo.YcostMapper;
import com.efrobot.robotstore.baseapi.manager.pojo.fabo.*;
import com.efrobot.robotstore.manager.service.InfoService;
import com.efrobot.robotstore.util.*;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@RequestMapping({"/py"})
@RestController
public class PYForController {
    private static final Logger log = Logger.getLogger(PYForController.class);
    @Resource
    private InfoService infoService;
    private static final int SPLIT_COUNT = 65535;
    private static Workbook workBook;
    @Autowired
    private StudentMapper studentMapper;
    @Autowired
    private YcostMapper ycostMapper;

    @RequestMapping({"/testMonth"})
    @ResponseBody
    public Map<String, Object> testMonth() {
        System.out.println("======testMonth----");
        log.info("======testMonth:");
        /*List<Student> list = studentMapper.selectBySchoolAll();
        Map<String, Object> map = new HashMap<>();
        map.put("size", list.size());
        List<Ycost> costList = ycostMapper.listByProduct(Arrays.asList("人均办公租金杂费公摊", "培训服务占总销售比例", "基本税费率", "货物损耗率"));
        log.info("====ycostMapper.listByProduct:");
        map.put("costList",costList);*/
        return CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());
    }

    @RequestMapping({"/testMonthDataEntrys"})
    @ResponseBody
    public Map<String, Object> testMonthDataEntrys() {
        System.out.println("======testMonthDataEntrys----");
        log.info("======testMonthDataEntrys:");
        List<Student> list = studentMapper.selectBySchoolAll();
        Map<String, Object> map = new HashMap<>();
        map.put("size", list.size());
        List<Ycost> costList = ycostMapper.listByProduct(Arrays.asList("人均办公租金杂费公摊", "培训服务占总销售比例", "基本税费率", "货物损耗率"));
        log.info("====ycostMapper.listByProduct:");
        map.put("costList", costList);
        return map;
    }

    @RequestMapping({"/getZsaleInfoPage"})
    @ResponseBody
    public JSONObject getZsaleInfoPage(@RequestBody Zsale info) throws Exception {
        JSONObject jsonObject = new JSONObject();
        JSONObject obj = new JSONObject();
        PageInfo<Zsale> rows = null;
        rows = this.infoService.getZsaleInfoPage(info, info.getPageNumber(), info.getPageSize());
        String result = JSONObject.toJSONString(rows, new SerializerFeature[]{SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullNumberAsZero, SerializerFeature.WriteNullStringAsEmpty});
        jsonObject = JSONObject.parseObject(result);
        return jsonObject;
    }


    @RequestMapping({"/getStudentInfoPage"})
    @ResponseBody
    public JSONObject getStudentInfoPage(@RequestBody Student info) throws Exception {
        JSONObject jsonObject = new JSONObject();
        JSONObject obj = new JSONObject();
        PageInfo<Student> rows = null;
        rows = this.infoService.getStudentInfoPage(info, info.getPageNumber(), info.getPageSize());
        String result = JSONObject.toJSONString(rows, new SerializerFeature[]{SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullNumberAsZero, SerializerFeature.WriteNullStringAsEmpty});
        jsonObject = JSONObject.parseObject(result);
        return jsonObject;
    }

    @RequestMapping({"/getZsaleCount"})
    @ResponseBody
    public Map<String, Object> getZsaleCount() {
        return this.infoService.getZsaleCount();
    }

    @RequestMapping({"/test"})
    @ResponseBody
    public Map<String, Object> invitePage(@RequestBody JSONObject jo) throws Exception {
        System.out.print(jo);
        return null;
    }

    @RequestMapping({"/addZsale"})
    @ResponseBody
    public Map<String, Object> addZsale(@RequestBody Zsale info) {
        return this.infoService.addZsale(info);
    }

    @RequestMapping({"/addTrialRobot"})
    @ResponseBody
    public Map<String, Object> addTrialRobot(@RequestBody Zsale info) {
        return this.infoService.addTrialRobot(info);
    }

    @RequestMapping({"/addAgentRobot"})
    @ResponseBody
    public Map<String, Object> addAgentRobot(@RequestBody Zagents info) {
        return this.infoService.addAgentRobot(info);
    }

    @RequestMapping({"/editZsale"})
    @ResponseBody
    public Map<String, Object> editZsale(@RequestBody Zsale info) {
        return this.infoService.editZsale(info);
    }

    @RequestMapping({"/deletZsale"})
    @ResponseBody
    public Map<String, Object> deletZsale(@RequestBody Zsale info) {
        return this.infoService.deletZsale(info);
    }

    @RequestMapping({"/deletZagents"})
    @ResponseBody
    public Map<String, Object> deletZagents(@RequestBody Zagents info) {
        return this.infoService.deletZagents(info);
    }


    @RequestMapping({"/getZagentsInfoPage"})
    @ResponseBody
    public JSONObject getZagentsInfoPage(@RequestBody Zagents info) throws Exception {
        JSONObject jsonObject = new JSONObject();
        JSONObject obj = new JSONObject();
        PageInfo<Zagents> rows = null;
        rows = this.infoService.getZagentsInfoPage(info, info.getPageNumber(), info.getPageSize());
        String result = JSONObject.toJSONString(rows, new SerializerFeature[]{SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullNumberAsZero, SerializerFeature.WriteNullStringAsEmpty});
        jsonObject = JSONObject.parseObject(result);
        return jsonObject;
    }

    @RequestMapping({"/addZagents"})
    @ResponseBody
    public Map<String, Object> addZagents(@RequestBody Zagents info) {
        return this.infoService.addZagents(info);
    }

    @RequestMapping({"/editZagents"})
    @ResponseBody
    public Map<String, Object> editZagents(@RequestBody Zagents info) {
        return this.infoService.editZagents(info);
    }

    @RequestMapping({"/updateRobotStatus"})
    @ResponseBody
    public Map<String, Object> updateRobotStatus(@RequestBody Zrobot info) {
        return this.infoService.updateRobotStatus(info);
    }

    @RequestMapping({"/updateQuota"})
    @ResponseBody
    public Map<String, Object> updateQuota(@RequestBody Student info) {
        return this.infoService.updateQuota(info);
    }

    @RequestMapping({"/nextRemind"})
    @ResponseBody
    public Map<String, Object> nextRemind(@RequestBody Zsale info) {
        return this.infoService.nextRemind(info);
    }

    @RequestMapping({"/exportZagentsTemplate"})
    public void exportZagentsTemplate(HttpServletResponse res, HttpSession session) throws Exception {
        ServletOutputStream servletOutputStream = res.getOutputStream();
        res.setHeader("Content-disposition", "attachment; filename=zagent.xlsx");
        res.setContentType("application/msexcel");
        String pathfile = PYForController.class.getResource("/xls/zagent.xlsx").getPath();
        workBook = (Workbook) new XSSFWorkbook(new FileInputStream(new File(pathfile)));
        workBook.write((OutputStream) servletOutputStream);
        servletOutputStream.close();
    }


    @RequestMapping({"/exportZsaleTemplate"})
    public void exportZsaleTemplate(HttpServletResponse res, HttpSession session) throws Exception {
        ServletOutputStream servletOutputStream = res.getOutputStream();
        res.setHeader("Content-disposition", "attachment; filename=zsale.xlsx");
        res.setContentType("application/msexcel");
        String pathfile = PYForController.class.getResource("/xls/zsale.xlsx").getPath();
        workBook = (Workbook) new XSSFWorkbook(new FileInputStream(new File(pathfile)));
        workBook.write((OutputStream) servletOutputStream);
        servletOutputStream.close();
    }


    @RequestMapping({"/checkZagentsExcel"})
    @ResponseBody
    public Map<String, Object> checkZagentsExcel(MultipartFile file, HttpServletRequest request, HttpServletResponse response, HttpSession session) throws Exception {
        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        InputStream inputStream = file.getInputStream();


        int ignoreRows = 1;


        String filename = file.getOriginalFilename();
        String extensionName = filename.substring(filename.lastIndexOf(".") + 1);

        List<String[]> list = ExcelParsingUtil.dataProcess(inputStream, extensionName, ignoreRows, 4);
        String sns = "";
        for (String[] str : list) {
            sns = String.valueOf(sns) + "," + str[0];
            if (str[0].equals("")) {
                break;
            }
        }
        map.put("sns", sns.substring(1, sns.length()));
        return map;
    }


    @RequestMapping({"/importZagentsExcel"})
    @ResponseBody
    public Map<String, Object> importZagentsExcel(MultipartFile file, HttpServletRequest request, HttpServletResponse response, HttpSession session) throws Exception {
        InputStream inputStream = file.getInputStream();


        int ignoreRows = 1;


        String filename = file.getOriginalFilename();
        String extensionName = filename.substring(filename.lastIndexOf(".") + 1);

        List<String[]> list = ExcelParsingUtil.dataProcess(inputStream, extensionName, ignoreRows, 4);

        Map<String, Object> map = this.infoService.importZagentsExcel(list);
        return map;
    }

    @RequestMapping({"/importZsaleExcel"})
    @ResponseBody
    public Map<String, Object> importZsaleExcel(MultipartFile file, HttpServletRequest request, HttpServletResponse response, HttpSession session) throws Exception {
        InputStream inputStream = file.getInputStream();


        int ignoreRows = 1;


        String filename = file.getOriginalFilename();
        String extensionName = filename.substring(filename.lastIndexOf(".") + 1);

        List<String[]> list = ExcelParsingUtil.dataProcess(inputStream, extensionName, ignoreRows, 10);


        Map<String, Object> map = this.infoService.importZsaleExcel(list);

        return map;
    }

    @RequestMapping({"/checkZsaleExcel"})
    @ResponseBody
    public Map<String, Object> checkZsaleExcel(MultipartFile file, HttpServletRequest request, HttpServletResponse response, HttpSession session) throws Exception {
        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());

        InputStream inputStream = file.getInputStream();


        int ignoreRows = 1;


        String filename = file.getOriginalFilename();
        String extensionName = filename.substring(filename.lastIndexOf(".") + 1);

        List<String[]> list = ExcelParsingUtil.dataProcess(inputStream, extensionName, ignoreRows, 9);
        String sns = "";
        for (String[] str : list) {
            sns = String.valueOf(sns) + "," + str[0];
            if (str[0].equals("")) {
                break;
            }
        }
        map.put("sns", sns.substring(1, sns.length()));
        return map;
    }

    @RequestMapping({"/unbind"})
    @ResponseBody
    public Map<String, Object> unbind(@RequestBody JSONObject jo) {
        return this.infoService.unbound(jo);
    }

    @RequestMapping({"/unbindForFabo"})
    @ResponseBody
    public Map<String, Object> unbindForFabo(@RequestBody JSONObject jo) {
        return this.infoService.unbound(jo);
    }


    @RequestMapping({"/getYorderDetailsPage"})
    @ResponseBody
    public JSONObject getYorderDetailsPage(@RequestBody YorderDetails info) throws Exception {
        JSONObject jsonObject = new JSONObject();
        JSONObject obj = new JSONObject();
        PageInfo<YorderDetails> rows = null;
        rows = this.infoService.getYorderDetailsPage(info, info.getPageNumber(), info.getPageSize());
        String result = JSONObject.toJSONString(rows, new SerializerFeature[]{SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullNumberAsZero, SerializerFeature.WriteNullStringAsEmpty});
        jsonObject = JSONObject.parseObject(result);
        return jsonObject;
    }


    @RequestMapping({"/getYpaidDetailsPage"})
    @ResponseBody
    public JSONObject getYpaidDetailsPage(@RequestBody YpaidDetails info) throws Exception {
        JSONObject jsonObject = new JSONObject();
        JSONObject obj = new JSONObject();
        PageInfo<YpaidDetails> rows = null;
        rows = this.infoService.getYpaidDetailsPage(info, info.getPageNumber(), info.getPageSize());
        String result = JSONObject.toJSONString(rows, new SerializerFeature[]{SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullNumberAsZero, SerializerFeature.WriteNullStringAsEmpty});
        jsonObject = JSONObject.parseObject(result);
        return jsonObject;
    }


    @RequestMapping({"/getSimilarCustomer"})
    @ResponseBody
    public Map<String, Object> getSimilarCustomer(@RequestBody JSONObject jo) {
        return this.infoService.getSimilarCustomer(jo);
    }

    /**
     * 获取用户实收的信息
     */
    @RequestMapping({"/getCustomerBond"})
    @ResponseBody
    public Map<String, Object> getCustomerBond(@RequestBody JSONObject jo) {
        log.info("getCustomerBond");
        return this.infoService.getCustomerBond(jo);
    }


    @RequestMapping({"/deletYorderDetails"})
    @ResponseBody
    public Map<String, Object> deletYorderDetails(@RequestBody YorderDetails info) {
        return this.infoService.deletYorderDetails(info);
    }

    /**
     * 删除实收数据
     */
    @RequestMapping({"/deletYpaidDetails"})
    @ResponseBody
    public Map<String, Object> deletYpaidDetails(@RequestBody YpaidDetails info) {
        return this.infoService.deletYpaidDetails(info);
    }

    @RequestMapping({"/editYpaidDetails"})
    @ResponseBody
    public Map<String, Object> editYpaidDetails(@RequestBody YpaidDetails info) {
        return this.infoService.editYpaidDetails(info);
    }


    @RequestMapping({"/editYorderDetails"})
    @ResponseBody
    public Map<String, Object> editYorderDetails(@RequestBody YorderDetails info) {
        return this.infoService.editYorderDetails(info);
    }


    @RequestMapping({"/monthDataEntrys"})
    @ResponseBody
    public Map<String, Object> monthDataEntrys(@RequestBody YdepartProfit info) {
        System.out.println("======monthDataEntrys:");
        log.info("======monthDataEntrys:" + info.getMonth() + "|" + info.getDepartment());
        return this.infoService.monthDataEntrys(info);
    }

    @RequestMapping({"/getMonthDataEntrys"})
    @ResponseBody
    public Map<String, Object> getMonthDataEntrys(@RequestBody YdepartProfit info) {
        return this.infoService.getMonthDataEntrys(info);
    }

    @RequestMapping({"/copyYpaidDetails"})
    @ResponseBody
    public Map<String, Object> copyYpaidDetails(@RequestBody JSONObject jo) {
        return this.infoService.copyYpaidDetails(jo);
    }

    @RequestMapping({"/synchroYpaidDetails"})
    @ResponseBody
    public Map<String, Object> synchroYpaidDetails(@RequestBody JSONObject jo) {
        return this.infoService.synchroYpaidDetails(jo);
    }


    @RequestMapping({"/importYorderDetailsExcel"})
    @ResponseBody
    public Map<String, Object> importYorderDetailsExcel(MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();
        int ignoreRows = 0;
        String filename = file.getOriginalFilename();
        String extensionName = filename.substring(filename.lastIndexOf(".") + 1);

        List<String[]> list = ExcelParsingUtil.dataProcessOrder(inputStream, extensionName, ignoreRows, 52);

        Map<String, Object> map = this.infoService.importYorderDetailsExcel(list);

        return map;
    }


    @RequestMapping({"/importYpaidDetailsExcel"})
    @ResponseBody
    public Map<String, Object> importYpaidDetailsExcel(@RequestParam("username") String username,
                                                       @RequestParam("file") MultipartFile file) throws Exception {
        log.info("importYpaidDetailsExcel");
        InputStream inputStream = file.getInputStream();
        int ignoreRows = 0;
        String filename = file.getOriginalFilename();
        String extensionName = filename.substring(filename.lastIndexOf(".") + 1);
        List<String[]> list = ExcelParsingUtil.dataProcess(inputStream, extensionName, ignoreRows, 13, "y_paid_details");
        String userName = username;
        Map<String, Object> map = this.infoService.importYpaidDetailsExcel(list, userName);
        return map;
    }

    /**
     * 实收导出
     *
     * @param jo
     * @return
     * @throws Exception
     */
    @RequestMapping({"/exportPaidExcel"})
    @ResponseBody
    public void exportPaidExcel(@RequestBody JSONObject jo, HttpServletResponse res) throws Exception {
        log.info("exportPaidExcel  ");
        String startDate = jo.getString("startDate");
        String endDate = jo.getString("endDate");
        String customerName = jo.getString("customerName");
        try {
            List<YpaidDetails> paidDetailsList = this.infoService.selectPaidByDate(startDate, endDate, customerName);
            ServletOutputStream servletOutputStream = res.getOutputStream();
            res.setHeader("Content-disposition", "attachment; filename=paid-export-" + startDate + "-" + endDate + "-" + System.currentTimeMillis() + ".xlsx");
            res.setContentType("application/msexcel");
            //  创建Excel
            createPaidWorkbook(servletOutputStream, paidDetailsList);
        } catch (Exception e) {
            log.error("exportPaidExcel Exception: ", e);
            throw e;
        }

    }

    private void createPaidWorkbook(ServletOutputStream os, List<YpaidDetails> paidDetailsList) throws Exception {
//        String pathfile = PYForController.class.getResource("/xls/PaidDetail_model.xlsx").getPath();
        try {
            try (InputStream is = PageForController.class.getResourceAsStream("/xls/PaidDetail_model.xlsx")) {
                if (is == null) {
                    log.error("文件不存在于 classpath: /xls/order.xlsx");
                    return; // 或抛出业务异常
                }
                log.info("Excel 开始加载");
                workBook = new XSSFWorkbook(is); // 不需要强制类型转换
                log.info("Excel 加载成功");
            } catch (IOException e) {
                log.info("Excel 文件读取失败", e);
            } catch (Exception e) {
                log.info("Excel 格式无效", e);
            }
            Sheet sheet = workBook.getSheet("营销部实收明细汇总");

            // 创建日期格式样式
            CellStyle dateStyle = workBook.createCellStyle();
            CreationHelper createHelper = workBook.getCreationHelper();
            dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-mm-dd"));
            // 设置单元格样式以启用文本换行
            CellStyle wrapStyle = workBook.createCellStyle();
            wrapStyle.setWrapText(true);
//            唯一编码	日期	客户名称	收款金额	新签保证金	备注	部门	销售经理	是否机器人	"小学
//            幼教"	电话招商销售	销售分成比例	电话分成比例
            for (int i = 0; i < paidDetailsList.size(); i++) {
                YpaidDetails detail = paidDetailsList.get(i);
                int rowNum = i + 1;
                Row row = sheet.createRow(rowNum);
                Cell cell = row.createCell(0);
                cell.setCellValue(print2String(detail.getUniqueCode()));
                cell = row.createCell(1);
                if (null != detail.getCollectionDate()) {
                    cell.setCellValue(detail.getCollectionDate());
                    cell.setCellStyle(dateStyle);
                }
                cell = row.createCell(2);
                cell.setCellValue(print2String(detail.getCustomerName()));
                cell = row.createCell(3);
                cell.setCellValue(bigDecimalToString(detail.getAmount()));
                cell = row.createCell(4);
                cell.setCellValue(print2String(detail.getBond()));
                cell = row.createCell(5);
                cell.setCellValue(print2String(detail.getRemark()));
                cell = row.createCell(6);
                cell.setCellValue(print2String(detail.getDepartment()));

                cell = row.createCell(7);
                cell.setCellValue(print2String(detail.getSalesManager()));
                // 是否机器人
                cell = row.createCell(8);
                cell.setCellValue(print2String(detail.getExp3()));
                cell = row.createCell(9);
                String dpType = "-";
                if (!StringUtils.isEmpty(detail.getDepartment())) {
                    if (detail.getDepartment().startsWith("小学")) {
                        dpType = "小学";
                    } else if (detail.getDepartment().startsWith("幼教")) {
                        dpType = "幼教";
                    }
                }
                cell.setCellValue(dpType);
                cell = row.createCell(10);
                cell.setCellValue(print2String(detail.getPhoneSales()));
                cell = row.createCell(11);
                cell.setCellValue(bigDecimalToString(detail.getSalesCommissionRatio()) + "%");
                cell = row.createCell(12);
                cell.setCellValue(bigDecimalToString(detail.getPhoneCommissionRatio()) + "%");
            }
        } catch (Exception e) {
            log.error("createPaidWorkbook Exception: ", e);
        }
        workBook.write(os);
        os.close();
    }

    /**
     * 提成导出
     *
     * @param jo
     * @return
     * @throws Exception
     */
    @RequestMapping({"/exportCommissionExcel"})
    @ResponseBody
    public void exportCommissionExcel(@RequestBody JSONObject jo, HttpServletResponse res) throws Exception {
        String startDate = jo.getString("startDate");
        String endDate = jo.getString("endDate");
        try {
            List<CommissionDetail> commissionDetails = this.infoService.selectCommissionByDate(startDate, endDate);
            // 参与分成的人员
            List<Member> memberList = infoService.selectCommissionMembers(new Member());
            // 对人员进行分组排序
            Map<String, Map<String, List<Member>>> memberMap = groupAndSortMembers(memberList);
            ServletOutputStream servletOutputStream = res.getOutputStream();
            res.setHeader("Content-disposition", "attachment; filename=Commission-" + startDate + "-" + endDate + "-" + System.currentTimeMillis() + ".xlsx");
            res.setContentType("application/msexcel");
            //  创建Excel
            createCommissionWorkbook(servletOutputStream, commissionDetails, memberMap);
        } catch (Exception e) {
            log.error("exportCommissionExcel Exception: ", e);
            throw e;
        }

    }
    /**
     * 提成查询
     *
     * @param jo
     * @return
     * @throws Exception
     */
    @RequestMapping({"/getAllCommissions"})
    @ResponseBody
    public Map<String, Object> getAllCommissions(@RequestBody JSONObject jo) throws Exception {
        String startDate = jo.getString("startDate");
        String endDate = jo.getString("endDate");
        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());
        try {
            List<CommissionDetail> commissionDetails = this.infoService.selectCommissionByDate(startDate, endDate);
            // 参与分成的人员
            List<Member> memberList = infoService.selectCommissionMembers(new Member());
            // 对人员进行分组排序
            Map<String, Map<String, List<Member>>> memberMap = groupAndSortMembers(memberList);

            List<CommissionDto> commissionDtos = convertToCommissionDto(commissionDetails, memberMap);

            map.put("data", commissionDtos);

        } catch (Exception e) {
            log.error("getAllCommissions Exception: ", e);
            return CommonUtil.resultMsg("FAIL", "数据错误");
        }
        return map;
    }

    private List<CommissionDto> convertToCommissionDto(List<CommissionDetail> commissionDetails, Map<String, Map<String, List<Member>>> memberMap) {
        List<CommissionDto> dtoList = new ArrayList<>();

        // 将CommissionDetail按照member.name转为Map
        Map<String, CommissionDetail> commissionDetailMap = commissionDetails.stream()
                .filter(detail -> detail.getMember() != null && detail.getMember().getName() != null)
                .collect(Collectors.toMap(
                        detail -> detail.getMember().getName(),
                        detail -> detail,
                        (existing, replacement) -> existing
                ));

        // 处理各个分组的成员
        Map<String, List<Member>> normalGroupMap = memberMap.get("normalGroup");
        Map<String, List<Member>> specialGroupMap = memberMap.get("specialGroup");

        // 处理幼教组
        processGroupToDto(normalGroupMap.get("幼教"), commissionDetailMap, dtoList);

        // 处理小学组
        processGroupToDto(normalGroupMap.get("小学"), commissionDetailMap, dtoList);

        // 处理特殊幼教组
        processGroupToDto(specialGroupMap.get("幼教"), commissionDetailMap, dtoList);

        // 处理特殊小学组
        processGroupToDto(specialGroupMap.get("小学"), commissionDetailMap, dtoList);

        // 处理其他组
        processGroupToDto(normalGroupMap.get("其他"), commissionDetailMap, dtoList);

        // 处理特殊其他组
        processGroupToDto(specialGroupMap.get("其他"), commissionDetailMap, dtoList);

        return dtoList;
    }

    private void processGroupToDto(List<Member> members, Map<String, CommissionDetail> commissionDetailMap,
                                  List<CommissionDto> dtoList) {
        if (members == null || members.isEmpty()) {
            return ;
        }

        int serialNumber = 1;

        for (Member member : members) {
            CommissionDto dto = new CommissionDto();

            // 设置序号
            dto.setSerialNumber(serialNumber++);

            // 设置成员基本信息
            dto.setName(member.getName());
            dto.setDepartment(member.getDepartment());
            dto.setPosition(member.getPosition());
            dto.setLevel(member.getLevel());
            dto.setEntryDate(member.getEntryDate());
            dto.setResignedDate(member.getResignedDate());
            dto.setWorkArea(member.getWorkArea());

            // 获取对应的佣金详情
            CommissionDetail commissionDetail = commissionDetailMap.get(member.getName());

            // 设置佣金相关信息
            if (commissionDetail != null) {
                dto.setSalesAmount(commissionDetail.getSalesAmount());
                dto.setOrderAmount(commissionDetail.getOrderAmount());
                dto.setBaseSalary(null != commissionDetail.getBaseSalary() ? commissionDetail.getBaseSalary().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setPerformSalary(null != commissionDetail.getPerformSalary() ? commissionDetail.getPerformSalary().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setSalary(null != commissionDetail.getSalary() ? commissionDetail.getSalary().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setCommissionAmount(null != commissionDetail.getCommissionAmount() ? commissionDetail.getCommissionAmount().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setTotalSalary(null != commissionDetail.getTotalSalary() ? commissionDetail.getTotalSalary().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));

                // 设置备注信息
                dto.setRemark(generateCommissionRemark(commissionDetail));
            } else {
                // 如果没有佣金详情，设置默认值
                dto.setSalesAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setOrderAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setBaseSalary(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setPerformSalary(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setSalary(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setCommissionAmount(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setTotalSalary(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                dto.setRemark("本月无进款");
            }

            // 添加到结果列表
            dtoList.add(dto);
        }

    }

    /**
     * 按照岗位和部门分组并排序成员列表
     *
     * @param memberList 需要分组排序的成员列表
     * @return 分组排序后的成员Map，key为岗位组类型，value为按部门类型分组的成员列表
     */
    private Map<String, Map<String, List<Member>>> groupAndSortMembers(List<Member> memberList) {
        // 定义部门排序顺序
        final List<String> departmentOrder = Arrays.asList(
                "幼教事业一部", "幼教事业二部", "幼教事业三部",
                "小学事业一部", "小学事业二部", "小学事业三部", "线下网络运营部"
        );

        // 定义岗位排序顺序
        final List<String> positionOrder = Arrays.asList(
                "营销副总", "销售总监", "招商总监", "招商主管", "招商经理", "电话招商", "培训师", "运营经理"
        );

        // 特殊岗位组
        final List<String> specialPositions = Arrays.asList("电话招商", "培训师", "运营经理");

        // 结果Map，分为两组：普通岗位组和特殊岗位组
        Map<String, Map<String, List<Member>>> result = new HashMap<>();
        result.put("normalGroup", new HashMap<>());
        result.put("specialGroup", new HashMap<>());

        // 初始化部门类型子组
        for (String groupKey : result.keySet()) {
            result.get(groupKey).put("幼教", new ArrayList<>());
            result.get(groupKey).put("小学", new ArrayList<>());
            result.get(groupKey).put("其他", new ArrayList<>());
        }

        // 分组
        for (Member member : memberList) {
            // 确定岗位组
            String positionGroup = "normalGroup";
            if (member.getPosition() != null && specialPositions.contains(member.getPosition())) {
                positionGroup = "specialGroup";
            }

            // 确定部门组
            String departmentGroup = "其他";
            if (member.getDepartment() != null) {
                if (member.getDepartment().startsWith("幼教")) {
                    departmentGroup = "幼教";
                } else if (member.getDepartment().startsWith("小学")) {
                    departmentGroup = "小学";
                }
            }

            // 添加到对应分组
            result.get(positionGroup).get(departmentGroup).add(member);
        }

        // 对每个组内成员进行排序
        Comparator<Member> memberComparator = (a, b) -> {
            // 1. 首先按部门排序
            String deptA = a.getDepartment() != null ? a.getDepartment() : "";
            String deptB = b.getDepartment() != null ? b.getDepartment() : "";

            int deptIndexA = departmentOrder.indexOf(deptA);
            int deptIndexB = departmentOrder.indexOf(deptB);

            if (deptIndexA == -1) deptIndexA = Integer.MAX_VALUE;
            if (deptIndexB == -1) deptIndexB = Integer.MAX_VALUE;

            int deptCompare = Integer.compare(deptIndexA, deptIndexB);
            if (deptCompare != 0) {
                return deptCompare;
            }

            // 2. 然后按职位排序
            String posA = a.getPosition() != null ? a.getPosition() : "";
            String posB = b.getPosition() != null ? b.getPosition() : "";

            int posIndexA = positionOrder.indexOf(posA);
            int posIndexB = positionOrder.indexOf(posB);

            if (posIndexA == -1) posIndexA = Integer.MAX_VALUE;
            if (posIndexB == -1) posIndexB = Integer.MAX_VALUE;

            return Integer.compare(posIndexA, posIndexB);
        };

        // 应用排序到每个子组
        for (Map<String, List<Member>> departmentGroups : result.values()) {
            for (List<Member> members : departmentGroups.values()) {
                members.sort(memberComparator);
            }
        }

        return result;
    }

    private void createCommissionWorkbook(OutputStream os, List<CommissionDetail> commissionDetails, Map<String, Map<String, List<Member>>> memberMap) throws Exception {
        String pathfile = PYForController.class.getResource("/xls/Commission_model.xlsx").getPath();
        try {
            try (InputStream is = new FileInputStream("/Users/<USER>/IdeaProjects/cloud-robot-fawn-clock-managre/src/main/resources/xls/Commission_model.xlsx")) {
                if (is == null) {
                    log.error("文件不存在于 classpath: /xls/order.xlsx");
                    return; // 或抛出业务异常
                }
                log.info("Excel 开始加载");
                workBook = new XSSFWorkbook(is); // 不需要强制类型转换
                log.info("Excel 加载成功");
            } catch (IOException e) {
                log.info("Excel 文件读取失败", e);
            } catch (Exception e) {
                log.info("Excel 格式无效", e);
            }

            Sheet sheet = workBook.getSheet("绩效考核汇总表");

            // 创建日期格式样式
//            CellStyle dateStyle = workBook.createCellStyle();
//            CreationHelper createHelper = workBook.getCreationHelper();
//            dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-mm-dd"));
            // 设置单元格样式以启用文本换行
            CellStyle wrapStyle = workBook.createCellStyle();
            wrapStyle.setWrapText(true);

            // 将CommissionDetail按照member.name转为Map
            Map<String, CommissionDetail> commissionDetailMap = commissionDetails.stream()
                    .filter(detail -> detail.getMember() != null && detail.getMember().getName() != null)
                    .collect(Collectors.toMap(
                            detail -> detail.getMember().getName(),
                            detail -> detail,
                            (existing, replacement) -> existing
                    ));

            Map<String, List<Member>> normalGroupMap = memberMap.get("normalGroup");
            List<Member> youngEduMembers = normalGroupMap.get("幼教");
            // 开始行号
            int startRow = 2;
            startRow = buildBookRows(youngEduMembers, commissionDetailMap, sheet, startRow, wrapStyle);

            List<Member> smallEduMembers = normalGroupMap.get("小学");
            startRow = buildBookRows(smallEduMembers, commissionDetailMap, sheet, startRow, wrapStyle);

            Map<String, List<Member>> specialGroupMap = memberMap.get("specialGroup");
            List<Member> specialYEduMembers = specialGroupMap.get("幼教");
            startRow = buildBookRows(specialYEduMembers, commissionDetailMap, sheet, startRow, wrapStyle);
            List<Member> specialXEduMembers = specialGroupMap.get("小学");
            startRow = buildBookRows(specialXEduMembers, commissionDetailMap, sheet, startRow, wrapStyle);

            List<Member> otherEduMembers = normalGroupMap.get("其他");
            startRow = buildBookRows(otherEduMembers, commissionDetailMap, sheet, startRow, wrapStyle);


            List<Member> specialOEduMembers = specialGroupMap.get("其他");
            startRow = buildBookRows(specialOEduMembers, commissionDetailMap, sheet, startRow, wrapStyle);

        } catch (Exception e) {
            log.error("createCommissionWorkbook Exception: ", e);
        }

        workBook.write(os);
        os.close();
    }

    private int buildBookRows(List<Member> youngEduMembers, Map<String, CommissionDetail> commissionDetailMap, Sheet sheet, int startRow, CellStyle wrapStyle) {
        for (int i = 0; i < youngEduMembers.size(); i++) {
            Member member = youngEduMembers.get(i);
            CommissionDetail commissionDetail = commissionDetailMap.get(member.getName());
//                Member member = commissionDetail.getMember();
//                序号	部门	姓名	职位	等级	入职日期	是否正式员工	离职日期	实际进款	实际订单	基本工资	业绩绩效分值	绩效工资	工资	提成	运营奖励池提成	入园奖励	"运营--代理商团队销售
//                考核通过奖励"	运营差旅补贴及差旅报销	工资总额	备注
            Row row = sheet.getRow(startRow);
            if (row == null) {
                row = sheet.createRow(startRow);
            }
            startRow++;
            Cell cell = getRowCell(row, 0);
            cell.setCellValue(i + 1);
            cell = getRowCell(row, 1);
            cell.setCellValue(print2String(member.getDepartment()));
            cell = getRowCell(row, 2);
            cell.setCellValue(print2String(member.getName()));
            cell = getRowCell(row, 3);
            cell.setCellValue(print2String(member.getPosition()));
            cell = getRowCell(row, 4);
            cell.setCellValue(print2String(member.getLevel()));
            cell = getRowCell(row, 5);
            if (null != member.getEntryDate()) {
                cell.setCellValue(DateUtils.format3(member.getEntryDate()));
//                    cell.setCellStyle(dateStyle);

            }
            // 是否正式员工
            cell = getRowCell(row, 6);
            cell = getRowCell(row, 7);
            if (null != member.getResignedDate()) {
                cell.setCellValue(DateUtils.format3(member.getResignedDate()));
//                    cell.setCellStyle(dateStyle);
            }
            // 实际进款
            cell = getRowCell(row, 8);
            cell.setCellValue(null == commissionDetail ? "0.00" : bigDecimalToString(commissionDetail.getSalesAmount()));
            cell = getRowCell(row, 9);
            cell.setCellValue(null == commissionDetail ? "0.00" : bigDecimalToString(commissionDetail.getOrderAmount()));
            cell = getRowCell(row, 10);
            cell.setCellValue(null == commissionDetail ? "0.00" : bigDecimalToString(commissionDetail.getBaseSalary()));
            cell = getRowCell(row, 11);
            cell = getRowCell(row, 12);
            cell.setCellValue(null == commissionDetail ? "0.00" : bigDecimalToString(commissionDetail.getPerformSalary()));
            cell = getRowCell(row, 13);
            cell.setCellValue(null == commissionDetail ? "0.00" : bigDecimalToString(commissionDetail.getSalary()));
            cell = getRowCell(row, 14);
            cell.setCellValue(null == commissionDetail ? "0.00" : bigDecimalToString(commissionDetail.getCommissionAmount()));
            // 运营奖励池提成	入园奖励
            cell = getRowCell(row, 15);
            cell = getRowCell(row, 16);
            cell = getRowCell(row, 17);
            cell = getRowCell(row, 18);
            cell = getRowCell(row, 19);
            cell.setCellValue(null == commissionDetail ? "0.00" : bigDecimalToString(commissionDetail.getTotalSalary()));
            cell = getRowCell(row, 20);
            cell.setCellStyle(wrapStyle);
            // 处理备注
//                本月无进款
//                4月9日入职
//                4月9日离职
//                实收：185666.67（16666.67*3%+169000（电话招商成交）*2%)
//                订单：857813元，机器人660000*0.3%+配件196513*0.15%+运费1300不计入提成
//                退款提成扣除-20000*3%-47389*1.5%

            cell.setCellValue(generateCommissionRemark(commissionDetail));
            cell = getRowCell(row, 21);
            cell.setCellValue(print2String(member.getWorkArea()));
        }
        return startRow;
    }

    private Cell getRowCell(Row row, int cee) {
        if (row == null) {
            return null;
        }
        Cell cell = row.getCell(cee);
        return cell == null ? row.createCell(cee) : cell;
    }

    private String print2String(Object o) {
        return o == null ? "" : o.toString();
    }

    private String generateCommissionRemark(CommissionDetail commissionDetail) {
        if (null == commissionDetail) {
            return "本月无进款";
        }
        StringBuilder remark = new StringBuilder();

        // 1. 如果salesAmount=0&&orderAmount=0,只展示"本月无进款"
        if ((commissionDetail.getSalesAmount() == null || commissionDetail.getSalesAmount().compareTo(BigDecimal.ZERO) == 0) &&
                (commissionDetail.getOrderAmount() == null || commissionDetail.getOrderAmount().compareTo(BigDecimal.ZERO) == 0)) {
            return "本月无进款";
        }

        // 2. 如果是本月入职离职的,显示入职日期和离职日期
        Member member = commissionDetail.getMember();
        if (member != null) {
            if (member.getEntryDate() != null) {
                String entryMonth = DateUtils.format(member.getEntryDate(), "MM");
                String currentMonth = DateUtils.format(new Date(), "MM");
                if (entryMonth.equals(currentMonth)) {
                    remark.append(DateUtils.format(member.getEntryDate(), "MM月dd日")).append("入职").append((char) 10);
                }
            }

            if (member.getResignedDate() != null) {
                String leaveMonth = DateUtils.format(member.getResignedDate(), "MM");
                String currentMonth = DateUtils.format(new Date(), "MM");
                if (leaveMonth.equals(currentMonth)) {
                    remark.append(DateUtils.format(member.getResignedDate(), "MM月dd日")).append("离职").append((char) 10);
                }
            }
        }

        // 3. 实收信息处理
        if (commissionDetail.getSalesAmount() != null && commissionDetail.getSalesAmount().compareTo(BigDecimal.ZERO) > 0) {
            remark.append("实收：").append(commissionDetail.getSalesAmount()).append("（");

            // 按type分组处理commentList
            Map<String, List<CommissionComment>> commentsByType = new HashMap<>();
            if (commissionDetail.getCommentList() != null) {
                for (CommissionComment comment : commissionDetail.getCommentList()) {
                    if (Arrays.asList("配件", "机器人").contains(comment.getType())) {
                        continue;
                    }
                    String type = comment.getType() != null ? comment.getType() : "普通";
                    commentsByType.computeIfAbsent(type, k -> new ArrayList<>()).add(comment);
                }
            }

            // 进一步按ratio分组并合并金额
            Map<String, Map<BigDecimal, BigDecimal>> typeRatioAmountMap = new HashMap<>();

            for (Map.Entry<String, List<CommissionComment>> entry : commentsByType.entrySet()) {
                String type = entry.getKey();
                List<CommissionComment> comments = entry.getValue();

                Map<BigDecimal, BigDecimal> ratioAmountMap = new HashMap<>();
                for (CommissionComment comment : comments) {
                    if (comment.getAmount() == null || comment.getRatio() == null) continue;

                    BigDecimal ratio = comment.getRatio();
                    BigDecimal amount = comment.getAmount();

                    BigDecimal currentAmount = ratioAmountMap.getOrDefault(ratio, BigDecimal.ZERO);
                    ratioAmountMap.put(ratio, currentAmount.add(amount));
                }

                typeRatioAmountMap.put(type, ratioAmountMap);
            }

            // 展示合并后的数据
            boolean first = true;
            for (Map.Entry<String, Map<BigDecimal, BigDecimal>> typeEntry : typeRatioAmountMap.entrySet()) {
                String type = typeEntry.getKey();
                Map<BigDecimal, BigDecimal> ratioAmountMap = typeEntry.getValue();

                for (Map.Entry<BigDecimal, BigDecimal> ratioEntry : ratioAmountMap.entrySet()) {
                    BigDecimal ratio = ratioEntry.getKey();
                    BigDecimal amount = ratioEntry.getValue();

                    if (!first) remark.append("+");
                    first = false;

                    remark.append(amount.setScale(2, RoundingMode.HALF_UP));
                    if ("电话招商成交".equals(type)) {
                        remark.append("（电话招商成交）");
                    }
                    remark.append("*").append(ratio.multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)).append("%");
                }
            }

            remark.append("）").append((char) 10);
        }

        // 4. 订单信息处理
        if (commissionDetail.getOrderAmount() != null && commissionDetail.getOrderAmount().compareTo(BigDecimal.ZERO) > 0) {
            remark.append("订单：").append(commissionDetail.getOrderAmount().setScale(2, RoundingMode.HALF_UP)).append("元，");

            // 机器人和配件信息
            if (commissionDetail.getRobotAmount() != null && commissionDetail.getRobotAmount().compareTo(BigDecimal.ZERO) > 0) {
                remark.append("机器人").append(commissionDetail.getRobotAmount().setScale(2, RoundingMode.HALF_UP)).append("*")
                        .append(Optional.ofNullable(commissionDetail.getRobotRatio()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)).append("%");
            }

            if (commissionDetail.getPartsAmount() != null && commissionDetail.getPartsAmount().compareTo(BigDecimal.ZERO) > 0) {
                remark.append("+配件").append(commissionDetail.getPartsAmount().setScale(2, RoundingMode.HALF_UP)).append("*")
                        .append(Optional.ofNullable(commissionDetail.getPartRatio()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)).append("%");
            }

            // 运费信息
            if (commissionDetail.getFreightAmount() != null && commissionDetail.getFreightAmount().compareTo(BigDecimal.ZERO) > 0) {
                remark.append("+运费").append(commissionDetail.getFreightAmount().setScale(2, RoundingMode.HALF_UP)).append("不计入提成");
            }
            remark.append((char) 10);
        }

        // 5. 退款信息处理
        if (commissionDetail.getRefundList() != null && !commissionDetail.getRefundList().isEmpty()) {
            // 按commissionRatio分组
            Map<BigDecimal, BigDecimal> refundsByRatio = new HashMap<>();
            for (CommissionRefund refund : commissionDetail.getRefundList()) {
                if (refund.getRefundAmount() == null || refund.getCommissionRatio() == null) continue;

                BigDecimal currentAmount = refundsByRatio.getOrDefault(refund.getCommissionRatio(), BigDecimal.ZERO);
                refundsByRatio.put(refund.getCommissionRatio(), currentAmount.add(refund.getRefundAmount()));
            }

            if (!refundsByRatio.isEmpty()) {
                remark.append("补做提成产生的退款提成扣除");
                boolean first = true;
                for (Map.Entry<BigDecimal, BigDecimal> entry : refundsByRatio.entrySet()) {
                    if (!first) remark.append("+");
                    first = false;

                    remark.append(entry.getValue()).append("*")
                            .append(entry.getKey().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP)).append("%");
                }
            }
        }

        return remark.toString();
    }

    private String bigDecimalToString(BigDecimal bigDecimal) {
        return bigDecimal == null ? "0.00" : bigDecimal.setScale(2, RoundingMode.HALF_UP).toString();
    }

    @RequestMapping({"/importYcollectionExcel"})
    @ResponseBody
    public Map<String, Object> importYcollectionExcel(MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();


        int ignoreRows = 1;


        String filename = file.getOriginalFilename();
        String extensionName = filename.substring(filename.lastIndexOf(".") + 1);

        List<String[]> list = ExcelParsingUtil.dataProcess(inputStream, extensionName, ignoreRows, 15);

        Map<String, Object> map = this.infoService.importYcollectionExcel(list);
        return map;
    }

    @RequestMapping({"/exportDepartmentalProfit"})
    public void exportDepartmentalProfit(HttpServletResponse res, HttpSession session) throws Exception {
        ServletOutputStream servletOutputStream = res.getOutputStream();
        res.setHeader("Content-disposition", "attachment; filename=DepartmentalProfit.xlsx");
        res.setContentType("application/msexcel");
        String mounthDateStr = DateUtils.format(new Date(), "yyyy-MM");
        YdepartProfit yd1 = this.infoService.selectByDepartAndMonth("线下网络一部", mounthDateStr);
        YdepartProfit yd2 = this.infoService.selectByDepartAndMonth("线下网络二部", mounthDateStr);
        YdepartProfit yd3 = this.infoService.selectByDepartAndMonth("大客户部", mounthDateStr);
        YdepartProfit yd4 = this.infoService.selectByDepartAndMonth("新媒体电商部", mounthDateStr);
        YdepartProfit yd5 = this.infoService.selectByDepartAndMonth("线下网络三部", mounthDateStr);
        createDepartmentalProfitWorkbook((OutputStream) servletOutputStream, yd1, yd2, yd3, yd4, yd5);
    }


    @RequestMapping({"/exportMarketingProfit"})
    public void exportMarketingProfit(HttpServletResponse res, HttpSession session) throws Exception {
        ServletOutputStream servletOutputStream = res.getOutputStream();
        res.setHeader("Content-disposition", "attachment; filename=MarketingProfit.xlsx");
        res.setContentType("application/msexcel");
        String mounthDateStr = DateUtils.format(new Date(), "yyyy-MM");
        Map<String, Object> map = this.infoService.getMarketingProfit(mounthDateStr);
        createMarketingProfitWorkbook((OutputStream) servletOutputStream, map);
    }


    @RequestMapping({"/exportYorderDetailsList"})
    @ResponseBody
    public Map<String, Object> exportYorderDetailsList(@RequestBody YorderDetails info) throws Exception {
        Map<String, Object> map = CommonUtil.resultMsg(ResultMessage.SUCCESS.getKey(),
                ResultMessage.SUCCESS.getValue());
        List<YorderDetails> list = this.infoService.selectByParms(info);
        map.put("data", list);
        return map;
    }

    @RequestMapping({"/exportYorderDetailsXml"})
    public void exportYorderDetailsXml(HttpServletResponse res, HttpServletRequest request) throws Exception {
        YorderDetails info = new YorderDetails();
        info.setIfSynchro(request.getParameter("ifSynchro"));
        info.setShippingDateStart(request.getParameter("shippingDateStart"));
        info.setShippingDateEnd(request.getParameter("shippingDateEnd"));
        info.setExp1(request.getParameter("exp1"));
        ServletOutputStream servletOutputStream = res.getOutputStream();
        res.setHeader("Content-disposition", "attachment; filename=order.xlsx");
        res.setContentType("application/msexcel");
        log.info("====exportYorderDetailsXml:");
        List<YorderDetails> list = this.infoService.selectByParms(info);
        if (list == null || list.size() == 0) {
            // 当数据为空时，返回提示信息
            res.setContentType("text/plain;charset=UTF-8");
            res.setHeader("Content-disposition", "noValue");
            // 使用 OutputStream 代替 Writer
            OutputStream out = res.getOutputStream();
            String message = "未查询到符合条件的数据，请检查查询条件。";
            out.write(message.getBytes("UTF-8"));
            out.flush();
            out.close();
        } else {
            log.info("list:::::" + list.size());
            createYorderDetailsListWorkbook((OutputStream) servletOutputStream, list, false);
        }
    }

    @RequestMapping({"/exportYorderDetailsDiscount"})
    public void exportYorderDetailsDiscount(HttpServletResponse res, HttpServletRequest request) throws Exception {
        log.info("exportYorderDetailsDiscount");
        YorderDetails info = new YorderDetails();
        info.setIfSynchro(request.getParameter("ifSynchro"));
        info.setShippingDateStart(request.getParameter("shippingDateStart"));
        info.setShippingDateEnd(request.getParameter("shippingDateEnd"));
        info.setExp1(request.getParameter("exp1"));
        ServletOutputStream servletOutputStream = res.getOutputStream();
        res.setHeader("Content-disposition", "attachment; filename=order.xlsx");
        res.setContentType("application/msexcel");
        List<YorderDetails> list = this.infoService.selectByParms(info);
        if (list == null || list.size() == 0) {
            // 当数据为空时，返回提示信息
            res.setContentType("text/plain;charset=UTF-8");
            res.setHeader("Content-disposition", "noValue");
            // 使用 OutputStream 代替 Writer
            OutputStream out = res.getOutputStream();
            String message = "未查询到符合条件的数据，请检查查询条件。";
            out.write(message.getBytes("UTF-8"));
            out.flush();
            out.close();
        } else {
            log.info("list:::::" + list.size());
            createYorderDetailsListWorkbook((OutputStream) servletOutputStream, list, true);
        }
    }

    public static void createDepartmentalProfitWorkbook(OutputStream os, YdepartProfit yd1, YdepartProfit yd2, YdepartProfit yd3, YdepartProfit yd4, YdepartProfit yd5) throws Exception {
        String pathfile = PYForController.class.getResource("/xls/DepartmentalProfit.xlsx").getPath();
        workBook = (Workbook) new XSSFWorkbook(new FileInputStream(new File(pathfile)));
        String[] sheets = {"线下网络一部", "线下网络二部", "大客户部", "新媒体电商部", "线下网络三部"};
        YdepartProfit[] ydepartProfits = {yd1, yd2, yd3, yd4, yd5};

        for (int i = 0; i < 5; i++) {
            Sheet sheet = workBook.getSheet(sheets[i]);
            YdepartProfit yd = ydepartProfits[i];
            if (yd != null) {
                Row row = sheet.getRow(2);
                Cell cell = row.getCell(2);
                cell.setCellValue(yd.getAdvertSaleProportion() + "%");
                row = sheet.getRow(3);
                cell = row.getCell(2);
                cell.setCellValue(yd.getNoAdvertSaleProportion() + "%");
                row = sheet.getRow(4);
                cell = row.getCell(2);
                cell.setCellValue("" + yd.getAdvertFeeProportion());
                row = sheet.getRow(5);
                cell = row.getCell(2);
                cell.setCellValue(yd.getCommissionProportion() + "%");
                row = sheet.getRow(6);
                cell = row.getCell(2);
                cell.setCellValue("" + yd.getPerCapitaSales());
                row = sheet.getRow(7);
                cell = row.getCell(2);
                cell.setCellValue("" + yd.getPerMonthIncome());
                row = sheet.getRow(8);
                cell = row.getCell(2);
                cell.setCellValue("" + yd.getTravelPerFee());
                row = sheet.getRow(9);
                cell = row.getCell(2);
                cell.setCellValue("" + yd.getRentPerFee());
                row = sheet.getRow(10);
                cell = row.getCell(2);
                cell.setCellValue(yd.getFreightProportion() + "%");
                row = sheet.getRow(11);
                cell = row.getCell(2);
                cell.setCellValue(yd.getTrainProportion() + "%");
                row = sheet.getRow(12);
                cell = row.getCell(2);
                cell.setCellValue(yd.getBasicTaxRate() + "%");
                row = sheet.getRow(13);
                cell = row.getCell(2);
                cell.setCellValue(yd.getRebateProportion() + "%");
                row = sheet.getRow(14);
                cell = row.getCell(2);
                cell.setCellValue(yd.getLossRate() + "%");
                row = sheet.getRow(15);
                cell = row.getCell(2);
                cell.setCellValue(yd.getIncidentalProportion() + "%");
                row = sheet.getRow(16);
                cell = row.getCell(2);
                cell.setCellValue(yd.getGrossMargin() + "%");
                row = sheet.getRow(17);
                cell = row.getCell(2);
                cell.setCellValue("" + yd.getTotalCost());
                row = sheet.getRow(18);
                cell = row.getCell(2);
                cell.setCellValue("" + yd.getSalesVolume());
                row = sheet.getRow(19);
                cell = row.getCell(2);
                cell.setCellValue("" + yd.getOperateFee());
                row = sheet.getRow(1);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getNetRealProfit());
                row = sheet.getRow(2);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getAdvertSales());
                row = sheet.getRow(3);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getNoAdvertSales());
                row = sheet.getRow(4);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getAdvertFee());
                row = sheet.getRow(5);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getCommissionAmount());
                row = sheet.getRow(6);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getTotalBaseSalary());
                row = sheet.getRow(7);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getMonthIncome());
                row = sheet.getRow(8);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getTravelFee());
                row = sheet.getRow(9);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getPeopleNum());
                row = sheet.getRow(10);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getFreightAmount());
                row = sheet.getRow(11);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getTrainAmount());
                row = sheet.getRow(12);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getTaxRate());
                row = sheet.getRow(13);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getRebateAmount());
                row = sheet.getRow(14);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getLossAmount());
                row = sheet.getRow(15);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getIncidentalAmount());
                row = sheet.getRow(16);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getGrossProfit());
                row = sheet.getRow(17);
                cell = row.getCell(4);
                cell.setCellValue(yd.getNetProfitMargin() + "%");
                row = sheet.getRow(18);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getNetProfit());
                row = sheet.getRow(19);
                cell = row.getCell(4);
                cell.setCellValue("" + yd.getDepartmentProportion());
            }
        }


        workBook.write(os);
        os.close();
    }


    public static void createMarketingProfitWorkbook(OutputStream os, Map<String, Object> map) throws Exception {
        String pathfile = PYForController.class.getResource("/xls/MarketingProfit.xlsx").getPath();
        workBook = (Workbook) new XSSFWorkbook(new FileInputStream(new File(pathfile)));
        Sheet sheet = workBook.getSheet("Sheet1");

        Row row = sheet.getRow(1);
        Cell cell = row.getCell(2);
        cell.setCellValue(map.get("1").toString());

        row = sheet.getRow(2);
        cell = row.getCell(2);
        cell.setCellValue(map.get("2").toString());
        row = sheet.getRow(3);
        cell = row.getCell(2);
        cell.setCellValue(map.get("3").toString());
        row = sheet.getRow(4);
        cell = row.getCell(2);
        cell.setCellValue(map.get("4").toString());
        row = sheet.getRow(5);
        cell = row.getCell(2);
        cell.setCellValue(map.get("5").toString());
        row = sheet.getRow(6);
        cell = row.getCell(2);
        cell.setCellValue(map.get("6").toString());
        row = sheet.getRow(7);
        cell = row.getCell(2);
        cell.setCellValue(map.get("7").toString());
        row = sheet.getRow(8);
        cell = row.getCell(2);
        cell.setCellValue(map.get("8").toString());
        row = sheet.getRow(9);
        cell = row.getCell(2);
        cell.setCellValue(map.get("9").toString());

        row = sheet.getRow(1);
        cell = row.getCell(4);
        cell.setCellValue(map.get("10").toString());
        row = sheet.getRow(2);
        cell = row.getCell(4);
        cell.setCellValue(map.get("11").toString());
        row = sheet.getRow(3);
        cell = row.getCell(4);
        cell.setCellValue(map.get("12").toString());
        row = sheet.getRow(4);
        cell = row.getCell(4);
        cell.setCellValue(map.get("13").toString());
        row = sheet.getRow(5);
        cell = row.getCell(4);
        cell.setCellValue(map.get("14").toString());
        row = sheet.getRow(6);
        cell = row.getCell(4);
        cell.setCellValue(map.get("15").toString());
        row = sheet.getRow(7);
        cell = row.getCell(4);
        cell.setCellValue(map.get("16").toString());
        row = sheet.getRow(8);
        cell = row.getCell(4);
        cell.setCellValue(map.get("17").toString());


        workBook.write(os);
        os.close();
    }


    public void createYorderDetailsListWorkbook(OutputStream os, List<YorderDetails> fieldData, boolean isDiscount) {

        try {
            try (InputStream is = PageForController.class.getResourceAsStream("/xls/order.xlsx")) {
                if (is == null) {
                    log.error("文件不存在于 classpath: /xls/order.xlsx");
                    return; // 或抛出业务异常
                }
                log.info("Excel 开始加载");
                workBook = new XSSFWorkbook(is); // 不需要强制类型转换
                log.info("Excel 加载成功");
            } catch (IOException e) {
                log.info("Excel 文件读取失败", e);
            } catch (Exception e) {
                log.info("Excel 格式无效", e);
            }
            log.info("22222");
            int sheetNum = 0;
            int rows = 0;
            if (fieldData != null && fieldData.size() > 0) {
                rows = fieldData.size();
                if (rows % 65535 == 0) {
                    sheetNum = rows / 65535;
                } else {
                    sheetNum = rows / 65535 + 1;
                }
            } else {
                Sheet sheet = workBook.getSheet("Sheet1");
            }
            if (sheetNum == 0) {
                sheetNum = 1;
            }

            Map<String, Object> dictInfo = infoService.selectAllAgent();

            log.info(sheetNum);
            for (int i = 1; i <= sheetNum; i++) {
                Sheet sheet = workBook.getSheet("Sheet" + i);

                Row row = sheet.getRow(0);
                Cell cell = row.getCell(0);
                for (int k = 0; k < ((rows < 65535) ? rows : 65535) && (
                        i - 1) * 65535 + k < rows; k++) {

                    row = sheet.createRow(k + 1);

                    YorderDetails m = fieldData.get((i - 1) * 65535 + k);
                    log.info(((i - 1) * 65535 + k) + "," + JSONObject.toJSONString(fieldData));
                    cell = row.createCell(0);
                    cell.setCellValue(m.getUniqueCode());

                    cell = row.createCell(1);
                    cell.setCellValue(DateUtils.format3(m.getShippingDate()));

                    cell = row.createCell(2);
                    cell.setCellValue(m.getDepartment());

                    cell = row.createCell(3);
                    cell.setCellValue(m.getSalesman());

                    cell = row.createCell(4);
                    cell.setCellValue(m.getOrderNo());

                    cell = row.createCell(5);
                    cell.setCellValue(m.getClientProperty());

                    cell = row.createCell(6);
                    cell.setCellValue(m.getOrderType());

                    cell = row.createCell(7);
                    cell.setCellValue(m.getShippingCompanyCode());

                    cell = row.createCell(8);
                    cell.setCellValue(m.getCompany());

                    cell = row.createCell(9);
                    cell.setCellValue(m.getProductCode());

                    cell = row.createCell(10);
                    cell.setCellValue(m.getProductName());

                    cell = row.createCell(11);
                    cell.setCellValue(m.getRobotModel());

                    cell = row.createCell(12);
                    cell.setCellValue(m.getPackagingUnit());

                    cell = row.createCell(13);
                    cell.setCellValue(m.getNum().intValue());
                    boolean isNeedSet = true;

                    if (isDiscount) {
//                        elif item == "tax_unit_price" or item == "tax_price" or item == "pay_price":
//                    #获取打折比例
                        Float priceDiscount = (Float) dictInfo.get(m.getCompany());

                        if (priceDiscount != null) {
//                            str(round(float( m.getTaxUnitPrice())*priceDiscount/100, 2))
                            BigDecimal discount = BigDecimal.valueOf(priceDiscount);

                            // 计算结果
                            BigDecimal result = m.getTaxUnitPrice()
                                    .multiply(discount)
                                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP);
                            cell = row.createCell(14);
                            cell.setCellValue(result.toString());

                            cell = row.createCell(15);
                            cell.setCellValue(m.getTaxPrice().multiply(discount)
                                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());

                            cell = row.createCell(17);
                            cell.setCellValue(m.getPayPrice().multiply(discount)
                                    .divide(new BigDecimal("100"), 2, RoundingMode.HALF_UP).toString());

                            isNeedSet = false;
                        } else {
                            System.out.println("未找到对应的折扣信息");
                        }
                    }

                    if (isNeedSet) {
                        cell = row.createCell(14);
                        cell.setCellValue("" + m.getTaxUnitPrice());

                        cell = row.createCell(15);
                        cell.setCellValue("" + m.getTaxPrice());


                        cell = row.createCell(17);
                        cell.setCellValue("" + m.getPayPrice());
                    }


                    cell = row.createCell(16);
                    cell.setCellValue(m.getPaymentStatus());


                    cell = row.createCell(18);
                    cell.setCellValue(m.getConsignee1());

                    cell = row.createCell(19);
                    cell.setCellValue(m.getConsignee2());

                    cell = row.createCell(20);
                    cell.setCellValue(m.getConsigneePhone1());

                    cell = row.createCell(21);
                    cell.setCellValue(m.getConsigneePhone2());

                    cell = row.createCell(22);
                    cell.setCellValue(m.getConsigneeAddress());

                    cell = row.createCell(23);
                    cell.setCellValue(m.getOrderDemand());

                    cell = row.createCell(24);

                    String remark = DataCacheUtil.productCode_remark.get(m.getProductCode());

                    cell.setCellValue(m.getRemark());
                    if (remark != null) {
                        cell.setCellValue(String.valueOf(m.getRemark()) + remark);
                    }

                    cell = row.createCell(25);
                    cell.setCellValue(m.getCard());

                    cell = row.createCell(26);
                    cell.setCellValue(m.getCollectionOrNot());

                    cell = row.createCell(27);
                    cell.setCellValue(m.getCollectionAmount());

                    cell = row.createCell(28);
                    cell.setCellValue(m.getInvoiceOrNot());

                    cell = row.createCell(29);
                    cell.setCellValue(m.getInvoiceType());

                    cell = row.createCell(30);
                    cell.setCellValue(m.getInvoiceHeader());

                    cell = row.createCell(31);
                    cell.setCellValue(m.getPriceSystem());

                    cell = row.createCell(48);
                    cell.setCellValue(m.getAgentModelId() == null ? "" : String.valueOf(m.getAgentModelId()));
                    cell = row.createCell(49);
                    cell.setCellValue(m.getAgentId() == null ? "" : String.valueOf(m.getAgentId()));
                    cell = row.createCell(50);
                    cell.setCellValue(m.getSystemTag() == null ? "" : String.valueOf(m.getSystemTag()));

                    if (isDiscount) {
                        cell = row.createCell(51);
                        cell.setCellValue(String.valueOf(m.getTaxUnitPrice()));

                        BigDecimal tax_price = m.getTaxPrice();
                        cell = row.createCell(52);
                        cell.setCellValue(tax_price.divide(BigDecimal.valueOf(1.13), 2, RoundingMode.HALF_UP).toString());

                        cell = row.createCell(53);
                        cell.setCellValue(tax_price.divide(BigDecimal.valueOf(1.13), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(0.13)).toString());

                        cell = row.createCell(54);
                        cell.setCellValue(tax_price.toString());
                    }
                    log.info(m.getOrderNo() + ",,,");
                }
            }

            workBook.write(os);
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("---createYorderDetailsListWorkbook Exception: ", e);
            throw new RuntimeException(e);
        }
    }

    @RequestMapping({"/coursewareInfoUpload"})
    @ResponseBody
    public Map<String, Object> coursewareInfoUpload(@RequestBody JSONObject jo) {
        return this.infoService.coursewareInfoUpload(jo);
    }

    @RequestMapping({"/getCoursewareStatus"})
    @ResponseBody
    public Map<String, Object> getCoursewareStatus(@RequestBody JSONObject jo) {
        return this.infoService.getCoursewareStatus(jo);
    }


    @RequestMapping({"/importContractExcel"})
    @ResponseBody
    public Map<String, Object> importContractExcel(MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();


        int ignoreRows = 1;


        String filename = file.getOriginalFilename();
        String extensionName = filename.substring(filename.lastIndexOf(".") + 1);

        List<String[]> list = ExcelParsingUtil.dataProcess(inputStream, extensionName, ignoreRows, 34);

        Map<String, Object> map = this.infoService.importContractExcel(list);
        return map;
    }


    @RequestMapping({"/importInvoiceExcel"})
    @ResponseBody
    public Map<String, Object> importInvoiceExcel(MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();


        int ignoreRows = 1;


        String filename = file.getOriginalFilename();
        String extensionName = filename.substring(filename.lastIndexOf(".") + 1);

        List<String[]> list = ExcelParsingUtil.dataProcess(inputStream, extensionName, ignoreRows, 10);

        Map<String, Object> map = this.infoService.importInvoiceExcel(list);
        return map;
    }


    @RequestMapping({"/importBondExcel"})
    @ResponseBody
    public Map<String, Object> importBondExcel(MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();


        int ignoreRows = 1;


        String filename = file.getOriginalFilename();
        String extensionName = filename.substring(filename.lastIndexOf(".") + 1);

        List<String[]> list = ExcelParsingUtil.dataProcess(inputStream, extensionName, ignoreRows, 6);

        Map<String, Object> map = this.infoService.importBondExcel(list);
        return map;
    }

    @RequestMapping({"/reportContractPdf"})
    @ResponseBody
    public Map<String, Object> reportContractPdf(@RequestBody JSONObject jo) {
        return this.infoService.reportContractPdf(jo);
    }
}

